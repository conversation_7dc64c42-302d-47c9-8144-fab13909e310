#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def final_decode():
    """Decodificação final baseada nos padrões encontrados"""
    
    # Dados originais
    raw_data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê',
        'Nome': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Senha': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Obs': 'êèêïîæâêêèêïîæâêêèêïîæâêê'
    }
    
    print("=== DECODIFICAÇÃO FINAL ===\n")
    
    # Para o Grupo: usar shift -128 que deu "ADMINISTRA"
    grupo_decoded = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in raw_data['Grupo'])
    print(f"Grupo (shift -128): {grupo_decoded}")
    
    # Limpar caracteres não imprimíveis
    grupo_clean = ''.join(c for c in grupo_decoded if c.isprintable() and (c.isalnum() or c.isspace()))
    print(f"Grupo (limpo): '{grupo_clean.strip()}'")
    
    # Para Nome e Senha: vamos tentar diferentes abordagens
    print(f"\n--- Testando Nome/Senha ---")
    
    nome_raw = raw_data['Nome']
    
    # Tentar shift -96 que mostrou "kad"
    nome_shift96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in nome_raw)
    print(f"Nome (shift -96): {nome_shift96}")
    nome_clean96 = ''.join(c for c in nome_shift96 if c.isprintable() and (c.isalnum() or c.isspace()))
    print(f"Nome (shift -96, limpo): '{nome_clean96.strip()}'")
    
    # Tentar shift -128
    nome_shift128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in nome_raw)
    print(f"Nome (shift -128): {nome_shift128}")
    nome_clean128 = ''.join(c for c in nome_shift128 if c.isprintable() and (c.isalnum() or c.isspace()))
    print(f"Nome (shift -128, limpo): '{nome_clean128.strip()}'")
    
    # Vamos tentar uma abordagem diferente - focar apenas nos primeiros caracteres significativos
    print(f"\n--- Análise dos primeiros caracteres ---")
    
    # Pegar apenas os primeiros caracteres antes da repetição
    # Observando os dados, parece que há um padrão de repetição
    
    # Para o grupo, vamos pegar até onde faz sentido
    grupo_significativo = raw_data['Grupo'][:13]  # Até antes da repetição
    print(f"Grupo (parte significativa): {grupo_significativo}")
    
    grupo_sig_decoded = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in grupo_significativo)
    print(f"Grupo decodificado: '{grupo_sig_decoded}'")
    
    # Para nome, vamos tentar diferentes tamanhos
    for size in [3, 4, 5, 6, 7, 8]:
        nome_part = nome_raw[:size]
        nome_part_decoded = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in nome_part)
        if nome_part_decoded.isprintable() and nome_part_decoded.isalpha():
            print(f"Nome ({size} chars): '{nome_part_decoded}'")
    
    # Tentar também com shift -128 para nome
    for size in [3, 4, 5, 6, 7, 8]:
        nome_part = nome_raw[:size]
        nome_part_decoded = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in nome_part)
        if nome_part_decoded.isprintable() and nome_part_decoded.replace(' ', '').isalpha():
            print(f"Nome shift-128 ({size} chars): '{nome_part_decoded}'")
    
    print(f"\n" + "="*50)
    print("CREDENCIAIS MAIS PROVÁVEIS:")
    print("="*50)
    
    # Baseado na análise, as credenciais mais prováveis são:
    final_grupo = grupo_sig_decoded.strip()
    
    # Para o nome, vamos com a opção mais limpa
    nome_3chars = nome_raw[:3]
    nome_final = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in nome_3chars)
    
    print(f"Grupo: {final_grupo}")
    print(f"Usuário: {nome_final}")
    print(f"Senha: {nome_final}")  # Assumindo que são iguais
    print("="*50)

if __name__ == "__main__":
    final_decode()
