try {
    $conn = New-Object -ComObject ADODB.Connection
    $conn.Open("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=DADOS.MDB")
    
    $rs = $conn.Execute("SELECT * FROM [PW~Usuarios]")
    
    Write-Host "=== Dados da tabela PW~Usuarios ==="
    
    while (-not $rs.EOF) {
        Write-Host "Registro:"
        for ($i = 0; $i -lt $rs.Fields.Count; $i++) {
            $fieldName = $rs.Fields.Item($i).Name
            $fieldValue = $rs.Fields.Item($i).Value
            
            # Tentar diferentes encodings
            if ($fieldValue -ne $null) {
                Write-Host "  $fieldName (original): $fieldValue"
                
                # Tentar converter de bytes para string
                try {
                    $bytes = [System.Text.Encoding]::GetEncoding("iso-8859-1").GetBytes($fieldValue)
                    $utf8String = [System.Text.Encoding]::UTF8.GetString($bytes)
                    Write-Host "  $fieldName (UTF8): $utf8String"
                } catch {
                    Write-Host "  $fieldName (UTF8): Erro na conversão"
                }
                
                # Tentar Windows-1252
                try {
                    $bytes = [System.Text.Encoding]::GetEncoding("windows-1252").GetBytes($fieldValue)
                    $utf8String = [System.Text.Encoding]::UTF8.GetString($bytes)
                    Write-Host "  $fieldName (Win1252): $utf8String"
                } catch {
                    Write-Host "  $fieldName (Win1252): Erro na conversão"
                }
            } else {
                Write-Host "  $fieldName : (null)"
            }
        }
        Write-Host "---"
        $rs.MoveNext()
    }
    
    $rs.Close()
    $conn.Close()
    
    Write-Host "Extração concluída!"
    
} catch {
    Write-Host "Erro ao acessar o banco de dados:"
    Write-Host $_.Exception.Message
}
