#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def decode_updated_data():
    """Decodifica os dados atualizados da tabela"""
    
    # Novos dados extraídos
    new_data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏìêêïîìêêïîìê',
        'Nome': 'ËÁÄìêêïîìêêïîìêêïîìêêïîìê',
        'Senha': 'ÍÁØõôñôöòññïîìêêïîìêêïîìê',  # Esta mudou!
        'Obs': 'êïîìêêïîìêêïîìêêïîìêêïîìê'
    }
    
    print("=== ANÁLISE DOS DADOS ATUALIZADOS ===\n")
    
    for campo, valor in new_data.items():
        print(f"--- {campo} ---")
        print(f"Valor: {valor}")
        print(f"Comprimento: {len(valor)}")
        print(f"Códigos ASCII: {[ord(c) for c in valor[:15]]}")  # Primeiros 15 chars
        
        # Tentar diferentes métodos de decodificação
        print("Decodificações:")
        
        # Shift -128 (que funcionou antes para o grupo)
        try:
            decoded_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in valor)
            clean_128 = ''.join(c for c in decoded_128 if c.isprintable() and (c.isalnum() or c.isspace()))
            if clean_128.strip():
                print(f"  Shift -128: '{clean_128.strip()}'")
        except:
            pass
        
        # Shift -96 (que funcionou para nome antes)
        try:
            decoded_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in valor)
            clean_96 = ''.join(c for c in decoded_96 if c.isprintable() and (c.isalnum() or c.isspace()))
            if clean_96.strip():
                print(f"  Shift -96: '{clean_96.strip()}'")
        except:
            pass
        
        # XOR com diferentes valores
        for xor_val in [128, 139, 171, 255]:
            try:
                decoded_xor = ''.join(chr(ord(c) ^ xor_val) for c in valor)
                clean_xor = ''.join(c for c in decoded_xor if c.isprintable() and (c.isalnum() or c.isspace()))
                if clean_xor.strip() and len(clean_xor.strip()) > 2:
                    print(f"  XOR {xor_val}: '{clean_xor.strip()}'")
            except:
                pass
        
        # Focar apenas nos primeiros caracteres significativos
        for length in [3, 4, 5, 6, 7, 8, 10]:
            parte = valor[:length]
            
            # Shift -128
            try:
                decoded = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in parte)
                if decoded.isprintable() and decoded.replace(' ', '').isalpha():
                    print(f"  Primeiros {length} chars (shift -128): '{decoded}'")
            except:
                pass
            
            # Shift -96
            try:
                decoded = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in parte)
                if decoded.isprintable() and decoded.replace(' ', '').isalpha():
                    print(f"  Primeiros {length} chars (shift -96): '{decoded}'")
            except:
                pass
        
        print()
    
    print("="*60)
    print("ANÁLISE ESPECÍFICA DA NOVA SENHA")
    print("="*60)
    
    senha = new_data['Senha']
    print(f"Nova senha codificada: {senha}")
    print(f"Códigos ASCII: {[ord(c) for c in senha]}")
    
    # A senha mudou, então vamos tentar decodificar com mais cuidado
    print("\nTentativas de decodificação da senha:")
    
    # Tentar diferentes tamanhos e métodos
    for length in range(3, 15):
        parte_senha = senha[:length]
        
        # Método 1: Shift -128
        try:
            decoded = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in parte_senha)
            if decoded.isprintable() and any(c.isalpha() for c in decoded):
                print(f"  {length} chars (shift -128): '{decoded}'")
        except:
            pass
        
        # Método 2: Shift -96
        try:
            decoded = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in parte_senha)
            if decoded.isprintable() and any(c.isalpha() for c in decoded):
                print(f"  {length} chars (shift -96): '{decoded}'")
        except:
            pass
        
        # Método 3: XOR com valores comuns
        for xor_val in [128, 139, 171, 255, 64, 32]:
            try:
                decoded = ''.join(chr(ord(c) ^ xor_val) for c in parte_senha)
                if decoded.isprintable() and any(c.isalpha() for c in decoded) and len(decoded.strip()) >= 3:
                    print(f"  {length} chars (XOR {xor_val}): '{decoded}'")
            except:
                pass
    
    print("\n" + "="*60)
    print("CREDENCIAIS DESCOBERTAS:")
    print("="*60)
    
    # Baseado nos padrões anteriores
    grupo = new_data['Grupo'][:13]  # Parte significativa
    grupo_decoded = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in grupo)
    
    nome = new_data['Nome'][:3]  # Primeiros 3 chars
    nome_decoded = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in nome)
    
    # Para a senha, vamos tentar o mesmo método do nome primeiro
    senha_parte = senha[:8]  # Tentar diferentes tamanhos
    senha_decoded_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in senha_parte)
    senha_decoded_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in senha_parte)
    
    print(f"Grupo: {grupo_decoded.strip()}")
    print(f"Usuário: {nome_decoded}")
    print(f"Senha (método 1): {senha_decoded_96}")
    print(f"Senha (método 2): {senha_decoded_128}")
    print("="*60)

if __name__ == "__main__":
    decode_updated_data()
