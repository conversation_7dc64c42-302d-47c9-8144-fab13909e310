#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def analyze_data_carefully():
    """Análise mais cuidadosa dos dados"""
    
    # Dados originais que obtivemos do PowerShell
    raw_data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê',
        'Nome': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Senha': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Obs': 'êèêïîæâêêèêïîæâêêèêïîæâêê'
    }
    
    print("=== ANÁLISE DETALHADA DOS DADOS ===\n")
    
    for campo, valor in raw_data.items():
        print(f"--- {campo} ---")
        print(f"Valor original: {valor}")
        print(f"Comprimento: {len(valor)}")
        
        # Mostrar códigos ASCII
        ascii_codes = [ord(c) for c in valor]
        print(f"Códigos ASCII: {ascii_codes}")
        
        # Tentar diferentes abordagens de decodificação
        print("Tentativas de decodificação:")
        
        # 1. Tentar como se fosse Windows-1252 mal interpretado
        try:
            # Converter para bytes usando latin-1 e depois decodificar como cp1252
            bytes_data = valor.encode('latin-1')
            decoded_1252 = bytes_data.decode('cp1252', errors='ignore')
            print(f"  CP1252: {decoded_1252}")
        except:
            print("  CP1252: Erro")
        
        # 2. Tentar diferentes shifts ASCII
        for shift in [-32, -64, -96, -128, -160, -192]:
            try:
                shifted = ''.join(chr(max(32, ord(c) + shift)) for c in valor)
                if any(c.isalpha() for c in shifted):
                    print(f"  Shift {shift}: {shifted}")
            except:
                pass
        
        # 3. Procurar por padrões específicos
        # Verificar se há repetições que podem indicar padding
        if len(set(valor[-10:])) <= 3:  # Se os últimos 10 chars têm poucos caracteres únicos
            # Pode ser padding, vamos focar no início
            inicio = valor[:15]  # Primeiros 15 caracteres
            print(f"  Início (sem padding): {inicio}")
            
            # Tentar decodificar só o início
            for xor_key in range(1, 256):
                try:
                    decoded = ''.join(chr(ord(c) ^ xor_key) for c in inicio)
                    if decoded.isprintable() and len(decoded.strip()) > 3:
                        # Verificar se parece com texto real
                        if any(word in decoded.upper() for word in ['ADMIN', 'USER', 'SENHA', 'PASSWORD', 'LOGIN']):
                            print(f"  XOR {xor_key} (início): {decoded}")
                except:
                    pass
        
        print()

def try_common_passwords():
    """Tenta decodificar assumindo senhas comuns"""
    
    # Dados dos campos Nome e Senha (que são idênticos)
    encoded_user_pass = 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê'
    
    # Senhas comuns para testar
    common_passwords = [
        'admin', 'ADMIN', 'Admin',
        'password', 'PASSWORD', 'Password',
        'senha', 'SENHA', 'Senha',
        'usuario', 'USUARIO', 'Usuario',
        '123456', '12345', '1234',
        'root', 'ROOT', 'Root',
        'guest', 'GUEST', 'Guest'
    ]
    
    print("=== TENTATIVA COM SENHAS COMUNS ===\n")
    
    for password in common_passwords:
        # Tentar encontrar a chave XOR que transformaria o texto codificado na senha
        if len(password) <= len(encoded_user_pass):
            # Pegar os primeiros caracteres correspondentes
            encoded_part = encoded_user_pass[:len(password)]
            
            # Calcular possível chave XOR
            possible_keys = []
            for i in range(len(password)):
                key = ord(encoded_part[i]) ^ ord(password[i])
                possible_keys.append(key)
            
            # Se todas as chaves são iguais, encontramos um padrão
            if len(set(possible_keys)) == 1:
                xor_key = possible_keys[0]
                # Testar decodificar todo o campo com esta chave
                try:
                    full_decoded = ''.join(chr(ord(c) ^ xor_key) for c in encoded_user_pass)
                    print(f"Senha testada: {password}")
                    print(f"Chave XOR: {xor_key}")
                    print(f"Resultado completo: {repr(full_decoded)}")
                    print(f"Resultado limpo: {full_decoded.strip()}")
                    print()
                except:
                    pass

def manual_analysis():
    """Análise manual dos padrões"""
    
    print("=== ANÁLISE MANUAL ===\n")
    
    # Vamos olhar para os dados de forma diferente
    grupo = 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê'
    nome = 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê'
    
    print("Observações:")
    print(f"1. Grupo tem 'GC' no meio: {grupo}")
    print(f"2. Nome e Senha são idênticos")
    print(f"3. Muitos caracteres se repetem no final")
    
    # Vamos tentar focar apenas nos primeiros caracteres únicos
    print(f"\nPrimeiros 10 chars do Grupo: {grupo[:10]}")
    print(f"Primeiros 10 chars do Nome: {nome[:10]}")
    
    # Tentar decodificar apenas estes
    for campo, valor in [("Grupo", grupo[:10]), ("Nome", nome[:10])]:
        print(f"\n--- {campo} (primeiros 10) ---")
        for xor_key in [128, 139, 171, 255, 64, 96, 32]:
            try:
                decoded = ''.join(chr(ord(c) ^ xor_key) for c in valor)
                if decoded.isprintable():
                    print(f"XOR {xor_key}: {decoded}")
            except:
                pass

if __name__ == "__main__":
    analyze_data_carefully()
    try_common_passwords()
    manual_analysis()
