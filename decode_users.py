#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def decode_access_data():
    """Tenta decodificar os dados que obtivemos do Access"""
    
    # Dados que obtivemos do PowerShell (codificados)
    raw_data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê',
        'Nome': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Senha': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Obs': 'êèêïîæâêêèêïîæâêêèêïîæâêê'
    }
    
    print("=== Dados originais (codificados) ===")
    for campo, valor in raw_data.items():
        print(f"{campo}: {valor}")
    
    print("\n=== Tentativas de decodificação ===")
    
    for campo, valor in raw_data.items():
        print(f"\n--- Campo: {campo} ---")
        print(f"Original: {valor}")
        
        # Tentar diferentes métodos de decodificação
        
        # Método 1: Converter para bytes e tentar diferentes encodings
        try:
            # Converter string para bytes usando latin-1
            bytes_data = valor.encode('latin-1')
            print(f"Bytes: {bytes_data}")
            
            # Tentar decodificar com diferentes encodings
            encodings = ['utf-8', 'cp1252', 'iso-8859-1', 'ascii']
            for encoding in encodings:
                try:
                    decoded = bytes_data.decode(encoding, errors='ignore')
                    if decoded != valor and decoded.strip():
                        print(f"  {encoding}: {repr(decoded)}")
                except:
                    pass
        except Exception as e:
            print(f"  Erro na conversão de bytes: {e}")
        
        # Método 2: Análise de padrões
        print(f"  Comprimento: {len(valor)}")
        print(f"  Caracteres únicos: {set(valor)}")
        
        # Método 3: Tentar XOR simples
        for key in range(1, 256):
            try:
                decoded = ''.join(chr(ord(c) ^ key) for c in valor)
                if decoded.isprintable() and any(c.isalpha() for c in decoded):
                    print(f"  XOR {key}: {repr(decoded)}")
            except:
                pass
        
        # Método 4: Shift de caracteres
        for shift in range(1, 26):
            try:
                decoded = ''.join(chr((ord(c) - shift) % 256) for c in valor)
                if decoded.isprintable() and any(c.isalpha() for c in decoded):
                    print(f"  Shift -{shift}: {repr(decoded)}")
            except:
                pass

def analyze_patterns():
    """Analisa padrões nos dados"""
    print("\n=== Análise de padrões ===")
    
    # Os dados que conseguimos
    data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê',
        'Nome': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Senha': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Obs': 'êèêïîæâêêèêïîæâêêèêïîæâêê'
    }
    
    # Verificar se há padrões comuns
    for campo, valor in data.items():
        print(f"\n{campo}:")
        print(f"  Primeiros 3 chars: {valor[:3]}")
        print(f"  Últimos 3 chars: {valor[-3:]}")
        print(f"  Códigos ASCII: {[ord(c) for c in valor[:10]]}")

def try_simple_substitution():
    """Tenta substituição simples baseada em padrões conhecidos"""
    print("\n=== Tentativa de substituição simples ===")
    
    # Dados originais
    grupo = 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê'
    nome = 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê'
    senha = 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê'
    
    # Tentar mapear caracteres comuns
    # Baseado no fato de que muitos caracteres se repetem
    
    # Criar um mapeamento baseado em frequência
    all_chars = grupo + nome + senha
    char_freq = {}
    for c in all_chars:
        char_freq[c] = char_freq.get(c, 0) + 1
    
    print("Frequência de caracteres:")
    for char, freq in sorted(char_freq.items(), key=lambda x: x[1], reverse=True):
        print(f"  '{char}' (ord {ord(char)}): {freq} vezes")
    
    # Tentar mapeamento para caracteres comuns em português/inglês
    common_chars = 'aeiouAEIOUnmrsldt'
    most_frequent = sorted(char_freq.items(), key=lambda x: x[1], reverse=True)
    
    print("\nTentativa de mapeamento:")
    mapping = {}
    for i, (char, freq) in enumerate(most_frequent[:len(common_chars)]):
        if i < len(common_chars):
            mapping[char] = common_chars[i]
            print(f"  '{char}' -> '{common_chars[i]}'")
    
    print("\nResultados com mapeamento:")
    for campo, valor in [('Grupo', grupo), ('Nome', nome), ('Senha', senha)]:
        mapped = ''.join(mapping.get(c, c) for c in valor)
        print(f"  {campo}: {mapped}")

if __name__ == "__main__":
    decode_access_data()
    analyze_patterns()
    try_simple_substitution()
