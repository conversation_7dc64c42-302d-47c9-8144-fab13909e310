#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def find_complete_password():
    """Encontra a senha completa analisando toda a string"""
    
    # Dados atualizados
    senha_codificada = 'ÍÁØõôñôöòññïîìêêïîìêêïîìê'
    
    print("=== ANÁLISE COMPLETA DA SENHA ===\n")
    print(f"Senha codificada: {senha_codificada}")
    print(f"Comprimento total: {len(senha_codificada)}")
    print(f"Códigos ASCII: {[ord(c) for c in senha_codificada]}")
    
    # Vamos analisar onde pode estar o fim real da senha
    print(f"\n--- Análise de padrões ---")
    
    # Verificar onde começam as repetições (que podem ser padding)
    chars = list(senha_codificada)
    print(f"Caracteres: {chars}")
    
    # Procurar por padrões repetitivos que indicam padding
    for i in range(len(chars)):
        char = chars[i]
        print(f"Posição {i}: '{char}' (ASCII {ord(char)})")
    
    print(f"\n--- Tentativas de decodificação por tamanho ---")
    
    # Tentar diferentes tamanhos para encontrar onde a senha real termina
    for length in range(3, len(senha_codificada) + 1):
        parte = senha_codificada[:length]
        
        # Método que funcionou antes: shift -96
        try:
            decoded_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in parte)
            if decoded_96.isprintable():
                print(f"  {length} chars (shift -96): '{decoded_96}'")
        except:
            pass
        
        # Método alternativo: shift -128
        try:
            decoded_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in parte)
            if decoded_128.isprintable():
                print(f"  {length} chars (shift -128): '{decoded_128}'")
        except:
            pass
    
    print(f"\n--- Análise de onde termina a senha real ---")
    
    # Vamos procurar onde começam os caracteres repetitivos
    # que provavelmente são padding
    
    # Primeiro, vamos ver os códigos ASCII para identificar padrões
    ascii_codes = [ord(c) for c in senha_codificada]
    print(f"Códigos ASCII completos: {ascii_codes}")
    
    # Procurar por sequências repetitivas
    for i in range(len(ascii_codes) - 2):
        if ascii_codes[i] == ascii_codes[i+2]:  # Padrão de repetição
            print(f"Possível início de padding na posição {i}")
            
            # Testar senha até esta posição
            senha_real = senha_codificada[:i]
            if len(senha_real) >= 3:
                decoded_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in senha_real)
                decoded_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in senha_real)
                
                print(f"  Senha até posição {i} (shift -96): '{decoded_96}'")
                print(f"  Senha até posição {i} (shift -128): '{decoded_128}'")
    
    print(f"\n--- Análise específica dos caracteres ---")
    
    # Vamos analisar cada caractere individualmente
    for i, char in enumerate(senha_codificada):
        ascii_val = ord(char)
        
        # Tentar decodificar com shift -96
        if ascii_val >= 96:
            decoded_96 = chr(ascii_val - 96)
        else:
            decoded_96 = char
            
        # Tentar decodificar com shift -128
        if ascii_val >= 128:
            decoded_128 = chr(ascii_val - 128)
        else:
            decoded_128 = char
            
        print(f"  Pos {i}: '{char}' (ASCII {ascii_val}) -> shift-96: '{decoded_96}' | shift-128: '{decoded_128}'")
    
    print(f"\n--- Identificação da senha completa ---")
    
    # Baseado na análise, vamos tentar identificar onde a senha real termina
    # Procurar por onde começam caracteres que não fazem sentido como senha
    
    senha_chars = []
    for i, char in enumerate(senha_codificada):
        ascii_val = ord(char)
        
        if ascii_val >= 96:
            decoded = chr(ascii_val - 96)
            if decoded.isprintable() and (decoded.isalnum() or decoded in '!@#$%^&*()_+-=[]{}|;:,.<>?'):
                senha_chars.append(decoded)
                print(f"  Caractere válido {i}: '{decoded}'")
            else:
                print(f"  Possível fim da senha na posição {i}")
                break
        else:
            print(f"  Caractere especial na posição {i}: '{char}'")
            if char.isprintable() and (char.isalnum() or char in '!@#$%^&*()_+-=[]{}|;:,.<>?'):
                senha_chars.append(char)
            else:
                break
    
    senha_completa = ''.join(senha_chars)
    
    print(f"\n" + "="*50)
    print("SENHA COMPLETA DESCOBERTA:")
    print("="*50)
    print(f"Usuário: kad")
    print(f"Senha: {senha_completa}")
    print(f"Comprimento da senha: {len(senha_completa)} caracteres")
    print("="*50)

if __name__ == "__main__":
    find_complete_password()
