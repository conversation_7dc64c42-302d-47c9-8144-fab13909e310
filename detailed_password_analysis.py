#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def detailed_password_analysis():
    """Análise mais detalhada para encontrar a senha completa real"""
    
    senha_codificada = 'ÍÁØõôñôöòññïîìêêïîìêêïîìê'
    
    print("=== ANÁLISE DETALHADA DA SENHA COMPLETA ===\n")
    
    # Vamos analisar os padrões de repetição para identificar onde termina a senha real
    ascii_codes = [ord(c) for c in senha_codificada]
    print(f"Códigos ASCII: {ascii_codes}")
    
    # Procurar por padrões repetitivos que indicam padding/preenchimento
    print(f"\n--- Identificação de padrões repetitivos ---")
    
    # Verificar se há sequências que se repetem
    for start in range(len(ascii_codes) - 4):
        for length in range(2, 6):  # Sequências de 2 a 5 caracteres
            if start + length * 2 <= len(ascii_codes):
                seq1 = ascii_codes[start:start + length]
                seq2 = ascii_codes[start + length:start + length * 2]
                
                if seq1 == seq2:
                    print(f"Sequência repetida encontrada na posição {start}: {seq1}")
                    print(f"Possível fim da senha na posição {start}")
    
    print(f"\n--- Análise por método de decodificação ---")
    
    # Vamos tentar diferentes abordagens para encontrar onde a senha real termina
    
    # Método 1: Procurar onde começam caracteres não imprimíveis ou inválidos
    print("Método 1 - Shift -96 (caracteres válidos):")
    senha_chars_96 = []
    for i, char in enumerate(senha_codificada):
        ascii_val = ord(char)
        if ascii_val >= 96:
            decoded = chr(ascii_val - 96)
            if decoded.isprintable() and decoded != ' ':
                senha_chars_96.append(decoded)
                print(f"  Pos {i}: '{decoded}' (válido)")
            else:
                print(f"  Pos {i}: '{decoded}' (inválido - possível fim)")
                break
        else:
            print(f"  Pos {i}: ASCII {ascii_val} < 96 (fim)")
            break
    
    senha_metodo1 = ''.join(senha_chars_96)
    print(f"Senha pelo método 1: '{senha_metodo1}'")
    
    # Método 2: Shift -128 (caracteres válidos)
    print(f"\nMétodo 2 - Shift -128 (caracteres válidos):")
    senha_chars_128 = []
    for i, char in enumerate(senha_codificada):
        ascii_val = ord(char)
        if ascii_val >= 128:
            decoded = chr(ascii_val - 128)
            if decoded.isprintable() and decoded.isalnum():
                senha_chars_128.append(decoded)
                print(f"  Pos {i}: '{decoded}' (válido)")
            else:
                print(f"  Pos {i}: '{decoded}' (inválido - possível fim)")
                break
        else:
            print(f"  Pos {i}: ASCII {ascii_val} < 128 (fim)")
            break
    
    senha_metodo2 = ''.join(senha_chars_128)
    print(f"Senha pelo método 2: '{senha_metodo2}'")
    
    # Método 3: Análise de padrões específicos
    print(f"\nMétodo 3 - Análise de padrões:")
    
    # Vamos ver se há um padrão claro de onde a senha termina
    # Observando os dados: [205, 193, 216, 245, 244, 241, 244, 246, 242, 241, 241, 239, 238, 236, 234, 234, 239, 238, 236, 234, 234, 239, 238, 236, 234]
    
    # Parece que a partir da posição 14 (ASCII 234) começam repetições
    # Vamos verificar isso
    
    # Procurar onde começam as repetições sistemáticas
    for i in range(len(ascii_codes) - 3):
        # Verificar se há um padrão repetitivo a partir desta posição
        if i + 6 < len(ascii_codes):
            chunk1 = ascii_codes[i:i+3]
            chunk2 = ascii_codes[i+3:i+6]
            if chunk1 == chunk2:
                print(f"Padrão repetitivo encontrado a partir da posição {i}")
                print(f"Chunk: {chunk1}")
                
                # A senha real provavelmente termina antes desta posição
                senha_real = senha_codificada[:i]
                
                # Decodificar com shift -96
                senha_decoded_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in senha_real)
                print(f"Senha até posição {i} (shift -96): '{senha_decoded_96}'")
                
                # Decodificar com shift -128
                senha_decoded_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in senha_real)
                print(f"Senha até posição {i} (shift -128): '{senha_decoded_128}'")
                
                break
    
    # Método 4: Análise manual baseada nos padrões observados
    print(f"\nMétodo 4 - Análise manual:")
    
    # Observando os dados, vemos que:
    # - Posições 0-2: 205, 193, 216 -> "max" (shift -96) ou "MAX" (shift -128)
    # - A partir da posição 14: começam repetições de 234, 239, 238, 236
    
    # Vamos testar diferentes pontos de corte
    for cut_point in [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]:
        parte = senha_codificada[:cut_point]
        
        # Shift -96
        decoded_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in parte)
        
        # Shift -128  
        decoded_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in parte)
        
        # Verificar se faz sentido como senha
        if decoded_96.isprintable() and len(decoded_96.strip()) > 0:
            print(f"  Corte em {cut_point} (shift -96): '{decoded_96}'")
        
        if decoded_128.isprintable() and len(decoded_128.strip()) > 0:
            print(f"  Corte em {cut_point} (shift -128): '{decoded_128}'")
    
    print(f"\n" + "="*60)
    print("CONCLUSÃO - SENHA COMPLETA:")
    print("="*60)
    
    # Baseado na análise, a senha mais provável é:
    # Vamos com o método que faz mais sentido
    
    # Analisando os padrões, parece que a senha real vai até onde não há repetições
    # A partir da posição 14, vemos repetições claras
    
    # Vamos testar até a posição 11 (antes das repetições claras)
    senha_final_candidata = senha_codificada[:11]
    senha_final_96 = ''.join(chr(ord(c) - 96) if ord(c) >= 96 else c for c in senha_final_candidata)
    senha_final_128 = ''.join(chr(ord(c) - 128) if ord(c) >= 128 else c for c in senha_final_candidata)
    
    print(f"Usuário: kad")
    print(f"Senha (método shift -96): '{senha_final_96}'")
    print(f"Senha (método shift -128): '{senha_final_128}'")
    print(f"Comprimento: {len(senha_final_candidata)} caracteres codificados")
    print("="*60)

if __name__ == "__main__":
    detailed_password_analysis()
