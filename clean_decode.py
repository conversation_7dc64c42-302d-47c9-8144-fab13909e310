#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def clean_final_decode():
    """Decodificação limpa e final"""
    
    # Dados originais
    raw_data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê',
        'Nome': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Senha': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê'
    }
    
    print("=== CREDENCIAIS FINAIS DO BANCO DADOS.MDB ===\n")
    
    # GRUPO: Usar shift -128, mas limpar melhor
    grupo_raw = raw_data['Grupo']
    # Focar nos primeiros 10 caracteres que deram "ADMINISTRA"
    grupo_significativo = grupo_raw[:10]  # ÁÄÍÉÎÉÓÔÒÁ
    grupo_decoded = ''.join(chr(ord(c) - 128) for c in grupo_significativo)
    print(f"Grupo: {grupo_decoded}")
    
    # NOME/SENHA: Baseado na análise, os primeiros 3 caracteres são significativos
    nome_raw = raw_data['Nome']
    
    # Opção 1: shift -96 nos primeiros 3 chars = "kad"
    nome_3chars = nome_raw[:3]  # ËÁÄ
    nome_option1 = ''.join(chr(ord(c) - 96) for c in nome_3chars)
    
    # Opção 2: shift -128 nos primeiros 3 chars = "KAD"
    nome_option2 = ''.join(chr(ord(c) - 128) for c in nome_3chars)
    
    print(f"Usuário (opção 1): {nome_option1}")
    print(f"Usuário (opção 2): {nome_option2}")
    print(f"Senha (opção 1): {nome_option1}")
    print(f"Senha (opção 2): {nome_option2}")
    
    print("\n" + "="*50)
    print("RESULTADO MAIS PROVÁVEL:")
    print("="*50)
    print(f"Grupo: ADMINISTRA")
    print(f"Usuário: kad")
    print(f"Senha: kad")
    print("="*50)
    print("\nOU ALTERNATIVAMENTE:")
    print("="*50)
    print(f"Grupo: ADMINISTRA")
    print(f"Usuário: KAD")
    print(f"Senha: KAD")
    print("="*50)
    
    # Vamos também tentar ver se há outros padrões
    print(f"\n=== ANÁLISE ADICIONAL ===")
    
    # Verificar se os dados podem ser nomes de usuário comuns
    common_users = ['admin', 'root', 'user', 'guest', 'test']
    
    for user in common_users:
        if len(user) <= len(nome_raw):
            # Tentar calcular que shift seria necessário
            first_char_encoded = ord(nome_raw[0])  # Ë = 203
            first_char_target = ord(user[0].upper())
            shift_needed = first_char_encoded - first_char_target
            
            print(f"Para '{user}': shift necessário = {shift_needed}")
            
            if 0 < shift_needed < 256:
                try:
                    decoded = ''.join(chr(ord(c) - shift_needed) for c in nome_raw[:len(user)])
                    if decoded.upper() == user.upper():
                        print(f"  MATCH! Usuário: {user}, Shift: {shift_needed}")
                except:
                    pass

if __name__ == "__main__":
    clean_final_decode()
