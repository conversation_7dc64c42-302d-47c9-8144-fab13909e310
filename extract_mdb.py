#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import struct
import os

def read_mdb_raw(filename):
    """Lê o arquivo MDB e procura por strings legíveis"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
            
        print(f"Tamanho do arquivo: {len(data)} bytes")
        print("Procurando por strings legíveis...")
        
        # Procurar por padrões de texto
        strings = []
        current_string = ""
        
        for i, byte in enumerate(data):
            if 32 <= byte <= 126:  # Caracteres ASCII imprimíveis
                current_string += chr(byte)
            else:
                if len(current_string) >= 3:  # Strings com pelo menos 3 caracteres
                    strings.append((i - len(current_string), current_string))
                current_string = ""
        
        # Adicionar a última string se existir
        if len(current_string) >= 3:
            strings.append((len(data) - len(current_string), current_string))
        
        print(f"\nEncontradas {len(strings)} strings legíveis:")
        
        # Filtrar strings que podem ser relevantes para usuários/senhas
        relevant_keywords = ['user', 'usuario', 'senha', 'password', 'login', 'admin', 'pw', 'usuarios']
        
        print("\n=== Strings que podem ser relevantes ===")
        for pos, string in strings:
            string_lower = string.lower()
            if any(keyword in string_lower for keyword in relevant_keywords) or len(string) > 10:
                print(f"Posição {pos}: {repr(string)}")
        
        print("\n=== Todas as strings encontradas ===")
        for pos, string in strings[-50:]:  # Mostrar as últimas 50 strings
            print(f"Posição {pos}: {repr(string)}")
            
    except Exception as e:
        print(f"Erro ao ler arquivo: {e}")

def search_table_data(filename, table_name="PW~Usuarios"):
    """Procura especificamente por dados da tabela"""
    try:
        with open(filename, 'rb') as f:
            data = f.read()
        
        # Procurar pelo nome da tabela
        table_bytes = table_name.encode('ascii', errors='ignore')
        table_pos = data.find(table_bytes)
        
        if table_pos != -1:
            print(f"Tabela '{table_name}' encontrada na posição {table_pos}")
            
            # Examinar dados ao redor da tabela
            start = max(0, table_pos - 500)
            end = min(len(data), table_pos + 1000)
            
            region = data[start:end]
            
            print("\nDados ao redor da tabela:")
            # Tentar diferentes encodings
            for encoding in ['ascii', 'latin-1', 'cp1252', 'utf-8']:
                try:
                    decoded = region.decode(encoding, errors='ignore')
                    print(f"\n--- Encoding {encoding} ---")
                    print(repr(decoded))
                    break
                except:
                    continue
        else:
            print(f"Tabela '{table_name}' não encontrada diretamente")
            
            # Procurar por variações
            variations = [b'PW', b'Usuarios', b'usuarios', b'USUARIOS']
            for var in variations:
                pos = data.find(var)
                if pos != -1:
                    print(f"Encontrado '{var.decode()}' na posição {pos}")
                    
    except Exception as e:
        print(f"Erro: {e}")

if __name__ == "__main__":
    filename = "DADOS.MDB"
    
    if os.path.exists(filename):
        print("=== Análise do arquivo MDB ===")
        read_mdb_raw(filename)
        
        print("\n" + "="*50)
        print("=== Procura específica pela tabela ===")
        search_table_data(filename)
    else:
        print(f"Arquivo {filename} não encontrado!")
