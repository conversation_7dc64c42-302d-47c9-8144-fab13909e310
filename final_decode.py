#!/usr/bin/env python3
# -*- coding: utf-8 -*-

def decode_final():
    """Decodifica os dados finais baseado nos padrões encontrados"""
    
    # Dados originais codificados
    raw_data = {
        'Grupo': 'ÁÄÍÉÎÉÓÔÒÁGCÏæâêêèêïîæâêê',
        'Nome': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Senha': 'ËÁÄïîæâêêèêïîæâêêèêïîæâêê',
        'Obs': 'êèêïîæâêêèêïîæâêêèêïîæâêê'
    }
    
    print("=== RESULTADOS FINAIS DA DECODIFICAÇÃO ===\n")
    
    # Decodificar Grupo com XOR 128
    grupo_decoded = ''.join(chr(ord(c) ^ 128) for c in raw_data['Grupo'])
    print(f"Grupo (XOR 128): {grupo_decoded}")
    
    # Limpar o resultado do grupo (remover caracteres não imprimíveis)
    grupo_clean = ''.join(c for c in grupo_decoded if c.isprintable() and c.isalpha())
    print(f"Grupo (limpo): {grupo_clean}")
    
    # Decodificar Nome com XOR 139
    nome_decoded = ''.join(chr(ord(c) ^ 139) for c in raw_data['Nome'])
    print(f"\nNome (XOR 139): {nome_decoded}")
    
    # Limpar o resultado do nome
    nome_clean = ''.join(c for c in nome_decoded if c.isprintable() and c.isalpha())
    print(f"Nome (limpo): {nome_clean}")
    
    # Decodificar Senha com XOR 139
    senha_decoded = ''.join(chr(ord(c) ^ 139) for c in raw_data['Senha'])
    print(f"\nSenha (XOR 139): {senha_decoded}")
    
    # Limpar o resultado da senha
    senha_clean = ''.join(c for c in senha_decoded if c.isprintable() and c.isalpha())
    print(f"Senha (limpo): {senha_clean}")
    
    # Tentar também XOR 171 para Nome e Senha
    print(f"\n--- Alternativa com XOR 171 ---")
    nome_decoded_171 = ''.join(chr(ord(c) ^ 171) for c in raw_data['Nome'])
    nome_clean_171 = ''.join(c for c in nome_decoded_171 if c.isprintable() and c.isalpha())
    print(f"Nome (XOR 171, limpo): {nome_clean_171}")
    
    senha_decoded_171 = ''.join(chr(ord(c) ^ 171) for c in raw_data['Senha'])
    senha_clean_171 = ''.join(c for c in senha_decoded_171 if c.isprintable() and c.isalpha())
    print(f"Senha (XOR 171, limpo): {senha_clean_171}")
    
    print("\n" + "="*50)
    print("CREDENCIAIS DESCOBERTAS:")
    print("="*50)
    print(f"Grupo: {grupo_clean}")
    print(f"Usuário: {nome_clean}")
    print(f"Senha: {senha_clean}")
    print("="*50)
    
    # Verificar se as credenciais fazem sentido
    if "ADMIN" in grupo_clean.upper():
        print("✓ Grupo parece ser administrativo")
    
    if len(nome_clean) >= 4 and nome_clean.isalpha():
        print("✓ Nome de usuário parece válido")
    
    if len(senha_clean) >= 4 and senha_clean.isalpha():
        print("✓ Senha parece válida")

if __name__ == "__main__":
    decode_final()
