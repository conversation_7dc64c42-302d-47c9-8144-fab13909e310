try {
    $conn = New-Object -ComObject ADODB.Connection
    $conn.Open("Provider=Microsoft.ACE.OLEDB.12.0;Data Source=DADOS.MDB")
    
    $rs = $conn.Execute("SELECT * FROM [PW~Usuarios]")
    
    Write-Host "=== Dados da tabela PW~Usuarios ==="
    
    while (-not $rs.EOF) {
        Write-Host "Registro:"
        for ($i = 0; $i -lt $rs.Fields.Count; $i++) {
            $fieldName = $rs.Fields.Item($i).Name
            $fieldValue = $rs.Fields.Item($i).Value
            Write-Host "  $fieldName : $fieldValue"
        }
        Write-Host "---"
        $rs.MoveNext()
    }
    
    $rs.Close()
    $conn.Close()
    
    Write-Host "Extração concluída com sucesso!"
    
} catch {
    Write-Host "Erro ao acessar o banco de dados:"
    Write-Host $_.Exception.Message
    
    # Tentar com provider alternativo
    try {
        Write-Host "Tentando com provider alternativo..."
        $conn2 = New-Object -ComObject ADODB.Connection
        $conn2.Open("Provider=Microsoft.Jet.OLEDB.4.0;Data Source=DADOS.MDB")
        
        $rs2 = $conn2.Execute("SELECT * FROM [PW~Usuarios]")
        
        Write-Host "=== Dados da tabela PW~Usuarios (Provider alternativo) ==="
        
        while (-not $rs2.EOF) {
            Write-Host "Registro:"
            for ($i = 0; $i -lt $rs2.Fields.Count; $i++) {
                $fieldName = $rs2.Fields.Item($i).Name
                $fieldValue = $rs2.Fields.Item($i).Value
                Write-Host "  $fieldName : $fieldValue"
            }
            Write-Host "---"
            $rs2.MoveNext()
        }
        
        $rs2.Close()
        $conn2.Close()
        
    } catch {
        Write-Host "Erro também com provider alternativo:"
        Write-Host $_.Exception.Message
    }
}
